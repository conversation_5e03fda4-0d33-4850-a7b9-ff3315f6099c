import { FC, useEffect } from 'react';

import { useModel } from '@reduck/core';
import { useLocation } from 'react-router-dom';

import SpaceSelector from '@/components/SpaceSelector';
import { useManageUrlQuery } from '@/hooks/url';
import globalModel from '@/model/global';
import DataManageSpaceSelector from '@/pages/data-manage/components/SpaceSelector';
import MetricStorySpaceSelector from '@/pages/metric-story-manage/components/SpaceSelector';
import { DataManageUrls, MetricStoryManageUrls } from '@/routers/path';
import { MANAGE_NODE_ID_INIT } from '@/utils/constant';

const urlsDisableChangeSpace = [
  MetricStoryManageUrls.MetricStoryFormCreate,
  MetricStoryManageUrls.MetricStoryFormEdit,
  DataManageUrls.DataModelCreate,
  DataManageUrls.DataModelDetails,
  '/metric-story/edit',
];

const ManageArea: FC = () => {
  const { pathname } = useLocation();
  const { urlQuery, setUrlQuery } = useManageUrlQuery();
  const [{ authData }] = useModel([globalModel]);

  // 判断用户是否为管理员（任一权限为true即为管理员）
  const isAdmin = Boolean(
    authData?.isAdmin || authData?.isDataManager || authData?.isDomainExpert,
  );

  useEffect(() => {
    if (!urlQuery.nodeId) {
      // 管理员默认选择专家空间，非管理员不设置默认节点（显示提示信息）
      if (isAdmin) {
        setUrlQuery({
          nodeId: MANAGE_NODE_ID_INIT,
          spaceType: 'expert', // 确保设置默认的空间类型
        });
      }
    } else if (!urlQuery.spaceType) {
      // 如果有nodeId但没有spaceType，根据nodeId推断spaceType
      const spaceType =
        urlQuery.nodeId === MANAGE_NODE_ID_INIT ? 'expert' : 'expert'; // 默认为expert
      setUrlQuery({
        nodeId: urlQuery.nodeId,
        spaceType,
      });
    }
  }, [urlQuery.nodeId, urlQuery.spaceType, isAdmin, setUrlQuery]);

  const allowExpand = !urlsDisableChangeSpace.find(u => pathname.includes(u));

  // 根据路径决定使用哪个 SpaceSelector
  const renderSpaceSelector = () => {
    const commonProps = {
      allowExpand,
      allowChangeSpaceType: true,
      nodeId: urlQuery.nodeId || '',
      onChange: (nodeId: string, spaceType?: string) => {
        // 确保空间类型和节点ID都被正确设置
        const newQuery: { nodeId: string; spaceType?: string } = {
          nodeId: nodeId || '',
          spaceType: spaceType || urlQuery.spaceType,
        };

        // 如果是专家空间，确保nodeId不为空
        if (spaceType === 'expert' && !nodeId) {
          newQuery.nodeId = MANAGE_NODE_ID_INIT;
        }

        console.log('ManageArea onChange:', { nodeId, spaceType, newQuery }); // 调试日志
        setUrlQuery(newQuery);
      },
    };

    if (pathname.includes('/data-manage')) {
      return (
        <DataManageSpaceSelector
          {...commonProps}
          manageSpaceType={urlQuery.spaceType} // 传递当前的空间类型状态
        />
      );
    }

    if (pathname.includes('/metric-story')) {
      return <MetricStorySpaceSelector {...commonProps} />;
    }

    // 默认使用全局的 SpaceSelector
    return <SpaceSelector {...commonProps} />;
  };

  return renderSpaceSelector();
};

export default ManageArea;
