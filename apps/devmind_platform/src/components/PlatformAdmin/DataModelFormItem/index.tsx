import { FC } from 'react';

import { Select, Spin, Form } from '@arco-design/web-react';
import { useRequest } from 'ahooks';

import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import { TransferStatusOffline } from '@quality/common-apis/src/apis/api/metric_platform/model';

import { API_V1 } from '@/constants';
import { NodeManager } from '@/modules/node';

interface Props {
  nodeId: string;
  isCreate: boolean;
  spaceType?: string; // 添加可选的 spaceType prop
}

const DataModelFormItem: FC<Props> = props => {
  const { nodeId, isCreate, spaceType: propSpaceType } = props;
  // 优先使用传入的 spaceType，否则从 NodeManager 获取
  const spaceType =
    propSpaceType || NodeManager.getModule()?.getSpaceType(nodeId);

  const { data, loading } = useRequest(
    async () => {
      if (!nodeId || !spaceType) {
        return [];
      }

      const res = await metricDictServiceClient.GetModelListByCondition({
        version: API_V1,
        cond: { node_id: nodeId, space_type: spaceType },
      });

      if (res?.code !== 200) {
        return [];
      }

      const modelList = res.data?.data_list ?? [];

      if (isCreate) {
        return modelList.filter(
          // 新建时过滤下线的数据
          item => item.transfer_status !== TransferStatusOffline,
        );
      }

      return modelList;
    },
    { ready: Boolean(nodeId && spaceType) },
  );

  return (
    <Form.Item
      label="数据模型"
      field="model_id"
      rules={[{ required: true, message: '请选择数据模型' }]}>
      <Select
        placeholder="请选择数据模型"
        showSearch
        filterOption={(inputValue, option) =>
          option.props.children
            .toLowerCase()
            .indexOf(inputValue.toLowerCase()) >= 0
        }
        renderFormat={(option, _) => {
          return option?.children;
        }}
        loading={loading}
        notFoundContent={
          loading ? (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Spin style={{ margin: 12 }} />
            </div>
          ) : !nodeId || !spaceType ? (
            <div style={{ padding: 12, textAlign: 'center', color: '#ff4d4f' }}>
              缺少必要的空间信息，请检查当前页面状态
            </div>
          ) : data?.length === 0 ? (
            <div style={{ padding: 12, textAlign: 'center', color: '#999' }}>
              当前空间下暂无可用的数据模型
            </div>
          ) : null
        }>
        {!loading &&
          data?.map(({ display_name, id }) => (
            <Select.Option key={id} value={id}>
              {display_name}
            </Select.Option>
          ))}
      </Select>
    </Form.Item>
  );
};

export default DataModelFormItem;
