import { FC } from 'react';

import { Form, Input, Select, Radio, Tooltip } from '@arco-design/web-react';
import { IconQuestionCircle } from '@arco-design/web-react/icon';
import { useModal } from '@ebay/nice-modal-react';

import {
  DimensionTypeEnum,
  DimensionTypeID,
  DimensionTypeString,
  DimensionTypeBigint,
  DimensionTypeDouble,
  DimensionTypeArrayString,
  DimensionTypeArrayBIGINT,
  DimensionTypeArrayDOUBLE,
  DimensionTypeMap,
  DimensionTypeTimestamp,
  DimensionTypehour,
  DimensionTypeHOUR,
  DimensionTypedate,
  DimensionTypeDATE,
  DimensionTypemonth,
  DimensionTypeMONTH,
} from '@quality/common-apis/src/apis/engine/consts';
import { SqlEditor } from '@quality/common-components';

import DataModelFormItem from '@/components/PlatformAdmin/DataModelFormItem';
import { VisibleSpaceWithProjectSelector } from '@/components/PlatformAdmin/VisibleSpaceSelector/VisibleSpaceWithProjectSelector';
import UserSelect from '@/components/UserSelect';
import useManageUrlQuery from '@/hooks/url/useManageUrlQuery';
import { NodeManager } from '@/modules/node';
import {
  DimensionCategory,
  DimensionCategoryOptions,
  ScopeCategory,
} from '@/constants/data-modal';

import { DataFormat } from './DataFormat';

const START_TIME = '[start_time]';
const END_TIME = '[end_time]';

interface Props {
  nodeId: string;
}

const BaseInfoForm: FC<Props> = ({ nodeId }) => {
  const {
    urlQuery: { spaceType: urlSpaceType },
  } = useManageUrlQuery();
  const spaceType = urlSpaceType || NodeManager.getModule()?.getSpaceType(nodeId);
  const { form } = Form.useFormContext();

  const dimension_category: string = Form.useWatch('dimension_category', form);
  const scope_category: string = Form.useWatch('scope_category', form);
  const modal = useModal();
  const changeCategory = v => {
    form.setFieldValue('dimension_category', v);
    if (v === DimensionCategory.Function) {
      form.setFieldValue('scope_category', ScopeCategory.Public);
    } else {
      form.setFieldValue('scope_category', ScopeCategory.Model);
    }
  };
  // 获取初始化参数
  const isCreate = !modal.args!.dimensionId;
  return (
    <>
      <Form.Item
        label="名称"
        field="display_name"
        rules={[
          { required: true, message: '请输入维度名称' },
          {
            validator(field = '', callback) {
              if (field.length > 32) {
                callback('名称长度 1~32 个字符');
              }
            },
          },
        ]}>
        <Input placeholder="请输入维度名称" />
      </Form.Item>
      <Form.Item label="描述" field="description">
        <Input.TextArea placeholder="请输入描述" rows={3} />
      </Form.Item>
      <Form.Item
        label="负责人"
        field="owners"
        rules={[{ required: true, message: '请选择维度负责人' }]}>
        <UserSelect placeholder="请输入维度负责人" />
      </Form.Item>
      <Form.Item label="分类" field="dimension_category" required>
        {({ value }) => (
          <Radio.Group onChange={changeCategory} value={value}>
            {DimensionCategoryOptions.map(item => (
              <Radio key={item.value} value={item.value}>
                {item.label}
                <Tooltip position="top" content={item.tooltip}>
                  <IconQuestionCircle className="ml-1" />
                </Tooltip>
              </Radio>
            ))}
          </Radio.Group>
        )}
      </Form.Item>
      {dimension_category === DimensionCategory.Function && (
        <Form.Item label="作用范围" field="scope_category" required>
          <Radio.Group>
            <Radio value={ScopeCategory.Public}>全局</Radio>
            <Radio value={ScopeCategory.Model}>数据模型</Radio>
          </Radio.Group>
        </Form.Item>
      )}
      {scope_category !== ScopeCategory.Public && (
        <DataModelFormItem nodeId={nodeId} isCreate={isCreate} spaceType={spaceType} />
      )}
      {scope_category !== ScopeCategory.Public && (
        <Form.Item
          label="数据类型"
          field="data_type"
          rules={[{ required: true, message: '请选择数据类型' }]}>
          <Select
            placeholder="请选择数据类型"
            options={[
              DimensionTypeEnum,
              DimensionTypeID,
              DimensionTypeString,
              DimensionTypeBigint,
              DimensionTypeDouble,
              DimensionTypeArrayString,
              DimensionTypeArrayBIGINT,
              DimensionTypeArrayDOUBLE,
              DimensionTypeMap,
              DimensionTypeTimestamp,
              DimensionTypehour,
              DimensionTypeHOUR,
              DimensionTypedate,
              DimensionTypeDATE,
              DimensionTypemonth,
              DimensionTypeMONTH,
            ]}
          />
        </Form.Item>
      )}
      {scope_category !== ScopeCategory.Public && (
        <Form.Item label="数据格式" field="data_unit" required>
          <DataFormat />
        </Form.Item>
      )}

      <VisibleSpaceWithProjectSelector
        mode="dimension"
        visibleSpaceField="visible_space"
        visibleProjectConfigField="visible_project_config"
        visibleSpaceLabel="可见性"
        visibleProjectConfigLabel="可见区域"
      />
      <Form.Item
        label="表达式"
        field="caliber_sql"
        rules={[{ required: true, message: '请输入表达式' }]}>
        <SqlEditor />
      </Form.Item>
    </>
  );
};

export default BaseInfoForm;
