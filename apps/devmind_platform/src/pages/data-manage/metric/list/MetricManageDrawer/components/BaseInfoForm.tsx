import { FC } from 'react';

import { Form, Input } from '@arco-design/web-react';
import { useModal } from '@ebay/nice-modal-react';

import { SqlEditor } from '@quality/common-components';

import DataModelFormItem from '@/components/PlatformAdmin/DataModelFormItem';
import { VisibleSpaceWithProjectSelector } from '@/components/PlatformAdmin/VisibleSpaceSelector';
import UserSelect from '@/components/UserSelect';
import useManageUrlQuery from '@/hooks/url/useManageUrlQuery';
import { NodeManager } from '@/modules/node';

interface Props {
  nodeId: string;
}

const BaseInfoForm: FC<Props> = ({ nodeId }) => {
  const modal = useModal();
  const {
    urlQuery: { spaceType: urlSpaceType },
  } = useManageUrlQuery();
  const spaceType = urlSpaceType || NodeManager.getModule()?.getSpaceType(nodeId);

  // 拿到modal初始化的参数
  const isCreate = !modal.args!.metricId;
  return (
    <>
      <Form.Item
        label="名称"
        field="display_name"
        rules={[
          { required: true, message: '请输入名称' },
          {
            validator(field = '', callback) {
              if (field.length > 32) {
                callback('名称长度 1~32 个字符');
              }
            },
          },
        ]}>
        <Input placeholder="请输入名称" />
      </Form.Item>
      <Form.Item
        label="负责人"
        field="owners"
        rules={[{ required: true, message: '请选择指标负责人' }]}>
        <UserSelect placeholder="请输入指标负责人" />
      </Form.Item>
      <DataModelFormItem nodeId={nodeId} isCreate={isCreate} spaceType={spaceType} />
      <VisibleSpaceWithProjectSelector
        mode="metric"
        visibleSpaceField="visible_space"
        visibleProjectConfigField="visible_project_config"
        visibleSpaceLabel="可见性"
        visibleProjectConfigLabel="可见区域"
      />
      <Form.Item label="描述" field="description">
        <Input.TextArea placeholder="请输入描述" rows={3} />
      </Form.Item>
      <Form.Item
        label="表达式"
        field="caliber_sql"
        rules={[{ required: true, message: '请输入表达式' }]}>
        <SqlEditor />
      </Form.Item>
    </>
  );
};

export default BaseInfoForm;
