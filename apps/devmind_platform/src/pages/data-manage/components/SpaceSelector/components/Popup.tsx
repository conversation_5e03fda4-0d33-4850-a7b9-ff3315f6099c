import { FC, useState, useEffect, useCallback, useMemo } from 'react';

import { Input, Tree, Spin, Empty, Select } from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import { useModel } from '@reduck/core';
import { useRequest, useDebounce, useDebounceFn } from 'ahooks';
import cs from 'classnames';

import { NodeStatusAccessed } from '@quality/common-apis/src/apis/api/insight/consts';
import type { SearchNodeItem as SearchNodeItemType } from '@quality/common-apis/src/apis/api/insight/model';
import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import {
  SimpleUserInfo,
  ProductInfo,
} from '@quality/common-apis/src/apis/api/product/product_model';

import { API_V1 } from '@/constants';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';
import { NodeManager } from '@/modules/node';
import { DataManageUrls } from '@/routers/path';
import { treeFlat } from '@/utils/utils';

import SearchNodeItem from './SearchNodeItem';
import TreeNodeItem from './TreeNodeItem';

export interface TreeNode {
  id: string;
  pid: string;
  authorization_type: string;
  liaison_officers?: SimpleUserInfo[];
  name: string;
  children?: TreeNode[];
  parent: { id: string; name: string }[]; // 父空间名称列表, 面包屑展示
  color?: string;
}

export interface Props {
  spaceType: SpaceType;
  nodeId: string;
  treeData?: ProductInfo[];
  allowChangeSpaceType?: boolean; // 是否支持空间切换
  onClose: () => void;
  onChange?: (nodeId: string, spaceType: SpaceType) => void;
}

const expertSpaceOption = {
  label: '专家空间',
  value: SpaceType.Expert,
};

const Popup: FC<Props> = props => {
  const {
    spaceType,
    nodeId,
    treeData: propsTreeData,
    allowChangeSpaceType = false,
    onClose,
    onChange,
  } = props;

  const { virtualItem } = useVirtualSpaceType();
  const [{ authData, virtualTreeMaps, virtualSpaceList }] = useModel([
    globalModel,
  ]);

  // 判断用户是否为管理员（任一权限为true即为管理员）
  const isAdmin = Boolean(
    authData?.isAdmin || authData?.isDataManager || authData?.isDomainExpert,
  );

  // 获取虚拟组织类型选项（除了专家空间）
  const virtualSpaceOptions = useMemo(
    () =>
      (virtualSpaceList ?? [])
        .filter(item => item.Virtual && item.NameEn !== SpaceType.Expert)
        .map(item => ({
          label: item?.NameZh,
          value: item?.NameEn,
        })),
    [virtualSpaceList],
  );

  // 获取所有空间类型选项（管理员显示专家空间 + 虚拟组织类型，非管理员只显示虚拟组织）
  const allSpaceOptions = useMemo(() => {
    return isAdmin
      ? [expertSpaceOption, ...virtualSpaceOptions]
      : virtualSpaceOptions;
  }, [isAdmin, virtualSpaceOptions]);

  // 支持空间切换时 记录当前选中的空间类型
  // 根据用户权限和传入的空间类型来确定初始值
  const initialSpaceType = useMemo(() => {
    if (spaceType) {
      return spaceType;
    }
    // 如果没有传入空间类型，管理员默认专家空间，非管理员默认第一个虚拟空间
    if (isAdmin) {
      return SpaceType.Expert;
    }
    // 非管理员默认选择第一个可用的虚拟空间，如果没有则使用专家空间（但会被后续逻辑修正）
    return virtualSpaceOptions.length > 0
      ? (virtualSpaceOptions[0].value as SpaceType)
      : SpaceType.Expert;
  }, [spaceType, isAdmin, virtualSpaceOptions]);

  const [selectedSpaceType, setSelectedSpaceType] =
    useState<SpaceType>(initialSpaceType);

  // 高亮选中的节点
  const [highlightedNodeId, setHighlightedNodeId] = useState<string>(nodeId);

  // 同步初始空间类型的变化
  useEffect(() => {
    setSelectedSpaceType(initialSpaceType);
  }, [initialSpaceType]);

  // 同步父组件传入的节点ID变化
  useEffect(() => {
    setHighlightedNodeId(nodeId);
  }, [nodeId]);

  // 当空间类型初始化完成后，通知父组件（仅在初始化时执行一次）
  useEffect(() => {
    if (selectedSpaceType && (!spaceType || selectedSpaceType !== spaceType)) {
      // 通知父组件空间类型变化，清空节点ID
      onChange?.('', selectedSpaceType);
    }
  }, [selectedSpaceType]); // 只监听 selectedSpaceType 变化

  const handleSpaceTypeChange = (type: SpaceType) => {
    // 非管理员禁止选择专家空间
    if (!isAdmin && type === SpaceType.Expert) {
      return;
    }

    setSelectedSpaceType(type);
    setHighlightedNodeId(''); // 清空高亮节点

    // 对于所有空间类型，都先清空节点ID，让用户重新选择节点
    // 移除专家空间的特殊处理，让用户可以选择专家空间下的具体节点
    onChange?.('', type);
  };

  // 搜索空间
  const [queryName, setQueryName] = useState<string>('');
  const debounceQueryName = useDebounce(queryName);
  const {
    data: searchRes,
    loading: searchLoading,
    run,
  } = useRequest(
    async (nodeName: string) => {
      if (!nodeName) {
        return;
      }
      // 外部数据源，则直接走前端搜索
      if (propsTreeData) {
        const list = treeFlat<ProductInfo>(propsTreeData, 'children');
        return list
          .filter(({ node_name }) =>
            node_name.toLowerCase().includes(nodeName.toLowerCase()),
          )
          .map<SearchNodeItemType>(
            ({ node_id, node_name, authorization_type, liaison_officers }) => ({
              node_id,
              display_name: node_name,
              hint_content: '',
              authorization_type,
              node_status: NodeStatusAccessed,
              user_name: liaison_officers?.[0].username || '',
            }),
          );
      }
      // 正常走接口搜索
      const res = await metricDictServiceClient.SearchSpaceTree({
        version: API_V1,
        space_type: selectedSpaceType,
        content: nodeName,
      });
      const { data } = res.data;
      return data;
    },
    { manual: true },
  );
  const { run: debounceSearch } = useDebounceFn(run, { wait: 300 });
  const handleSearch = (v: string) => {
    setQueryName(v);
    debounceSearch(v);
  };

  // 空间树 - 根据选中的空间类型获取对应的树数据
  const treeData = useMemo(() => {
    if (propsTreeData) {
      return propsTreeData;
    }
    // 专家空间和虚拟空间都从 virtualTreeMaps 中获取数据
    return virtualTreeMaps[selectedSpaceType]?.treeData ?? [];
  }, [propsTreeData, selectedSpaceType, virtualTreeMaps]);

  const generatorTreeNodes = useCallback(
    (treeData: ProductInfo[]) =>
      treeData.map(item => {
        const { children, node_id } = item;
        const isHighlighted = highlightedNodeId === node_id;

        return (
          <Tree.Node
            key={node_id}
            dataRef={item}
            className={isHighlighted ? 'bg-blue-50' : ''}
            title={
              <TreeNodeItem
                activeNodeId={nodeId}
                nodeInfo={item}
                onClick={handleNodeChange}
              />
            }>
            {children?.length ? generatorTreeNodes(children) : null}
          </Tree.Node>
        );
      }),
    [nodeId, highlightedNodeId],
  );

  const handleNodeChange = async (nodeId: string) => {
    // 确保使用当前选中的空间类型
    const actualSpaceType = selectedSpaceType;

    // 在数据管理模块中，对于所有空间类型（包括专家空间），都需要处理节点信息
    const pathname = window.location.pathname;
    const isInManageModule = pathname.startsWith(DataManageUrls.DataManage);

    if (isInManageModule) {
      // 从树数据中查找节点信息
      const findNodeInTree = (
        nodes: ProductInfo[],
        targetId: string,
      ): ProductInfo | null => {
        for (const node of nodes) {
          if (node.node_id === targetId) {
            return node;
          }
          if (node.children) {
            const found = findNodeInTree(node.children, targetId);
            if (found) {
              return found;
            }
          }
        }
        return null;
      };

      const nodeInfo = findNodeInTree(treeData, nodeId);
      if (nodeInfo) {
        // 将节点信息添加到NodeManager中，并包含空间类型信息
        const nodeInfoWithSpaceType = {
          ...nodeInfo,
          spaceType: actualSpaceType,
        };
        NodeManager.getModule()?.addNodeRecord(nodeInfoWithSpaceType);

        // 同时添加到全局状态中
        addNodeRecord({ nodeId, nodeInfo: nodeInfoWithSpaceType });
      } else {
        // 如果在树数据中找不到节点信息，可能需要从API获取
        // 这种情况通常发生在专家空间或其他特殊情况
        console.warn(
          `Node ${nodeId} not found in tree data for space type ${actualSpaceType}`,
        );
      }
    }

    // 确保传递正确的空间类型
    onChange?.(nodeId, actualSpaceType);
    onClose?.();
    setHighlightedNodeId(nodeId); // 设置高亮节点
  };

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  useEffect(() => {
    if (nodeId) {
      const keys: string[] = [];
      const loop = (id: string) => {
        const pid = NodeManager.getModule()?.getNodeInfo(id)?.pid;
        if (pid) {
          keys.push(pid);
          loop(pid);
        }
      };
      loop(nodeId);
      setExpandedKeys(keys);
    }
  }, [nodeId, selectedSpaceType]);

  // 空间列表渲染
  const renderContent = () => {
    if (searchLoading) {
      return (
        <Spin
          size={36}
          block={true}
          className="flex justify-center items-center h-full"
        />
      );
    }

    // 展示搜索列表
    if (debounceQueryName && queryName) {
      return (
        <div className="overflow-y-auto h-full thin-scrollbar">
          {Array.isArray(searchRes) && searchRes?.length ? (
            searchRes.map(item => (
              <SearchNodeItem
                key={item.node_id}
                spaceType={selectedSpaceType}
                nodeInfo={item}
                onClick={handleNodeChange}
              />
            ))
          ) : (
            <Empty
              description="暂无数据"
              className="flex items-center h-full"
            />
          )}
        </div>
      );
    }

    // 非管理员且没有可用的空间数据时显示提示
    if (
      !isAdmin &&
      selectedSpaceType !== SpaceType.Expert &&
      treeData.length === 0
    ) {
      return (
        <Empty
          description="请先选择空间节点"
          className="flex items-center h-full"
        />
      );
    }

    // 展示全量数据
    return treeData.length ? (
      <Tree
        blockNode={true}
        className="thin-scrollbar"
        expandedKeys={expandedKeys}
        fieldNames={{
          key: 'node_id',
          title: 'node_name',
        }}
        virtualListProps={{ height: '100%' }}
        onExpand={setExpandedKeys}>
        {generatorTreeNodes(treeData)}
      </Tree>
    ) : (
      <Empty />
    );
  };
  const pathname = window.location.pathname;

  const options = allSpaceOptions;

  // 加载虚拟组织树数据的effect
  const [
    ,
    { getVirtualTreeNodes, getExpertTree, addVirtualTreeNodes, addNodeRecord },
  ] = useModel([globalModel]);

  useEffect(() => {
    // 如果选择了空间类型且还没有加载数据，则加载对应的树数据（包括专家空间）
    if (selectedSpaceType && !virtualTreeMaps[selectedSpaceType]?.isLoaded) {
      // 专家空间使用专门的方法获取数据
      if (selectedSpaceType === SpaceType.Expert) {
        getExpertTree()
          .then((data: any) => {
            const treeData = data || [];
            addVirtualTreeNodes(selectedSpaceType, treeData);
          })
          .catch((error: any) => {
            console.error('加载专家空间树数据失败:', error);
            addVirtualTreeNodes(selectedSpaceType, []);
          });
      } else {
        // 虚拟空间使用通用方法获取数据
        getVirtualTreeNodes(selectedSpaceType)
          .then((data: any) => {
            const treeData = data?.value || data || [];
            addVirtualTreeNodes(selectedSpaceType, treeData);
          })
          .catch((error: any) => {
            console.error('加载空间树数据失败:', error);
            addVirtualTreeNodes(selectedSpaceType, []);
          });
      }
    }
  }, [
    selectedSpaceType,
    virtualTreeMaps,
    getVirtualTreeNodes,
    getExpertTree,
    addVirtualTreeNodes,
  ]);

  // 判断是否在数据管理或指标故事管理模块
  const isInManageModule = pathname.startsWith(DataManageUrls.DataManage);

  return (
    <div className="bg-white border border-solid border-line2 rounded">
      <div className="border-b border-solid border-line2 p-5">
        {!allowChangeSpaceType && (
          <div className="space-y-3">
            <div className="text-base font-medium">{virtualItem?.NameZh}</div>
            <Input
              className="w-full"
              value={queryName}
              prefix={<IconSearch />}
              allowClear
              placeholder={`请搜索${virtualItem?.NameZh}空间名称`}
              onChange={handleSearch}
            />
          </div>
        )}

        {/* 数据管理和指标故事管理模块：显示两个下拉框在一行 */}
        {allowChangeSpaceType && isInManageModule && (
          <div className="space-y-3">
            <div className="flex gap-4">
              <div className="flex-1">
                <Select
                  className="w-full"
                  value={selectedSpaceType}
                  options={options}
                  placeholder="请选择空间类型"
                  onChange={handleSpaceTypeChange}
                  disabled={!isAdmin && selectedSpaceType === SpaceType.Expert}
                />
              </div>
              {/* 所有空间类型都显示搜索框，包括专家空间 */}
              <div className="flex-1">
                <Input
                  className="w-full"
                  value={queryName}
                  prefix={<IconSearch />}
                  allowClear
                  placeholder={
                    selectedSpaceType === SpaceType.Expert
                      ? '请搜索专家空间名称'
                      : '请搜索空间名称'
                  }
                  onChange={handleSearch}
                />
              </div>
            </div>
          </div>
        )}

        {/* 其他模块：保持原有设计 */}
        {allowChangeSpaceType && !isInManageModule && (
          <div className={cs(['flex justify-between gap-4'])}>
            <Select
              className="min-w-[222px]"
              value={selectedSpaceType}
              options={options}
              placeholder="请选择空间类型"
              onChange={handleSpaceTypeChange}
              disabled={!isAdmin && selectedSpaceType === SpaceType.Expert}
            />
            {/* 所有空间类型都显示搜索框，包括专家空间 */}
            <Input
              className="w-full"
              value={queryName}
              prefix={<IconSearch />}
              allowClear
              placeholder={
                selectedSpaceType === SpaceType.Expert
                  ? '请搜索专家空间名称'
                  : `请搜索${virtualItem?.NameZh}空间名称`
              }
              onChange={handleSearch}
            />
          </div>
        )}
      </div>
      {/* 专家空间也显示树形结构 */}
      <div
        className={cs(['h-[300px] min-w-[500px] max-w-[800px]', 'pt-4 px-5'])}>
        {renderContent()}
      </div>
    </div>
  );
};

export default Popup;
